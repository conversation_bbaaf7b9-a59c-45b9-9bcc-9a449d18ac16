.container {
      background: black;
      margin-top: 50px;
}

.header {
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  width: 100%;
  padding: 5px 0;
  background-color: rgb(255 255 0);
  color: black;
  margin-bottom: 40px;
}

.cardContainer {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  flex-wrap: wrap;
}

.card {
  width: 22%;
  background-color:none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  box-sizing: border-box;
}

.cardNumber {
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  font-weight: bold;
  color: black;
  background-color: rgb(255 255 0);
  border-radius: 50%;
  text-align: center;
  margin: 0 auto 10px auto;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cardImage {
  width: 50%;
  height: auto;
  border-radius: 8px;
  margin-bottom: 15px;
}
.imgs{
  margin-top:30px;
}
.cardContent {
  font-size: 20px;
  color: white;
  text-align: center;
}

@media (max-width: 768px) {
  .card {
    width: 48%; /* 2 cards per row */
  }
}

@media (max-width: 480px) {
  .card {
    width: 100%; /* 1 card per row */
  }
}
