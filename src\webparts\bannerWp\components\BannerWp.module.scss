.carouselWrapper {
  max-width: 100%;
  margin: 0 auto;
  position: relative;
}

.card {
  cursor: pointer;
}
.card[tabindex="-1"] {
  cursor: default;
  opacity: 0.6;
}

.carouselImage {
  width: 100%;
  height: auto;
  border-radius: 8px;
  object-fit: cover;
}

.swiperPagination {
  bottom: 10px !important;
  text-align: center;
}
.wrapper {
  background-color: #f0f0f0;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.header {
  text-align: center;
  background-color: yellow;
  color: black;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 28px;
  margin: 0;
}

.sixBoxes {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  width: 100%;
}

.row {
  display: flex;
  justify-content: center;
  gap: 20px;
  width: 100%;
}

.box {
  width: 200px;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #ffffff;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.boxHeader {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 0;
}

.row .box:hover {
  background-color: #f9f9f9;
  transform: scale(1.05);
  transition: all 0.3s ease-in-out;
}

/* Video section styles */
.videoSection {
  width: 100%;
  text-align: center;
  margin-top: 50px;
}

.videoSection h2 {
  margin-bottom: 20px;
}

.videoContainer {
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 100%; /* Optional: limits how big the video can grow */
  margin: 0 auto;
}

.videoContainer iframe,
.videoContainer video {
  width: 100%; /* Take full container width */
  height: 540px; /* Or set to a fixed height you prefer */
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}
.wrapper2 {
  padding: 20px;
  box-sizing: border-box;
  background-color: #f9f9f9;
}

.cardRow {
  display: flex;
  width: 100%;
  flex-wrap: wrap; /* Enables responsiveness */
}

.card {
  flex: 1 1 25%; /* Each card takes 25% width */
  max-width: 23%;
  padding: 16px;
  box-sizing: border-box;
  text-align: center;
  background-color: black;
  margin: 10px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-5px);
}

.cardImage {
  width: 40%;
  height: auto;
  border-radius: 6px;
  margin-bottom: 12px;
}

.cardTitle {
  font-size: 18px;
  font-weight: 600;
  color: rgb(255, 255, 0);
  margin: 0;
}



/* Responsive layout for tablets and phones */
@media (max-width: 1024px) {
  .card {
    flex: 1 1 50%;
    max-width: 50%;
  }
}

@media (max-width: 600px) {
  .card {
    flex: 1 1 100%;
    max-width: 100%;
  }
}
