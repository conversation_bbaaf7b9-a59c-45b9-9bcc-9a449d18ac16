import * as React from 'react';
import styles from './StartWp.module.scss';
import { useLocale } from '../../../common/useLocale';

type StartCard = { content: string };

const StartWP: React.FC = () => {
  const { strings } = useLocale();

  // ✅ Avoid duplicate FA injection on re-mounts
  React.useEffect(() => {
    const id = 'fa-5-15-4';
    if (!document.getElementById(id)) {
      const link = document.createElement('link');
      link.id = id;
      link.rel = 'stylesheet';
      link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';
      link.integrity = 'sha384-oy9iJK8O9zImLoJ9XzX3BxlFZXBxuGp9Bvv1jKfM5aqA2Vhxz7nEwAhlIVMCTqzD';
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    }
  }, []);

  // ✅ Localized header + cards with safe fallbacks
  const headerText: string =
    strings?.startHeader ?? 'How to start PLATON?';

  const cards: StartCard[] =
    strings?.startCards ?? [
      { content: 'Take your first step' },
      { content: 'Contact us, or we’ll reach out to you soon' },
      { content: 'Get the PLATON Basic private lesson' },
      { content: 'Get access to the PLATON environment and play around' }
    ];

  // Images stay the same for all languages
  const imgs = [
    'https://corptb.sharepoint.com/sites/ProjectEngLand/SiteAssets/PlatonImages/PLATON%20ICON/StartingPointIcons/icon-first-step.svg',
    'https://corptb.sharepoint.com/sites/ProjectEngLand/SiteAssets/PlatonImages/PLATON%20ICON/StartingPointIcons/icon-coming-soon.svg',
    'https://corptb.sharepoint.com/sites/ProjectEngLand/SiteAssets/PlatonImages/PLATON%20ICON/StartingPointIcons/icon-basic-lesson.svg',
    'https://corptb.sharepoint.com/sites/ProjectEngLand/SiteAssets/PlatonImages/PLATON%20ICON/StartingPointIcons/icon-access-platon-env.svg'
  ];

  return (
    <div className={styles.container}>
      {/* Header Section */}
      <div className={styles.header}>{headerText}</div>

      {/* Card Section */}
      <div className={styles.cardContainer}>
        {/* First card (arrow icon, no number) */}
        <div className={styles.card}>
          <div className={styles.cardNumber} aria-hidden="true">
            <i className="fa fa-arrow-down" />
          </div>
          <img
            src={imgs[0]}
            alt="Step 0"
            className={styles.cardImage}
          />
          <div className={styles.cardContent}>{cards[0]?.content}</div>
        </div>

        {/* Steps 1..3 */}
        {[1, 2, 3].map((n) => (
          <div className={styles.card} key={n}>
            <div className={styles.cardNumber}>{n}</div>
            <img
              src={imgs[n]}
              alt={`Step ${n}`}
              className={styles.cardImage}
            />
            <div className={styles.cardContent}>{cards[n]?.content}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default StartWP;
