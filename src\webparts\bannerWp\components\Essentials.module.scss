.wrapper {
  align-items: center;
  background-color: black;
  display: flex;
  flex-direction: column;
  padding: 20px;
  width: 100%;
  margin-top: 5%;
}

.header {
  text-align: center;
  background-color: rgb(255, 255, 0); /* Dark blue for the header */
  color: black;
  padding: 20px;
  border-radius: 8px;
  width: 100%; /* Full width */
  max-width: 100%; /* Ensure the header spans the full width */
  margin-bottom: 30px;
}

.header h1 {
  font-size: 28px;
  margin: 0;
}

.sixBoxes {
  display: flex;
  flex-direction: column;
  gap: 20px; /* Gap between the rows */
  align-items: center; /* Center all boxes horizontally */
  width: 100%;
}

.row {
  display: flex;
  justify-content: center; /* Center the boxes in the row */
  gap: 20px; /* Gap between each box */
  width: 100%;
}

.box {
  width: 200px; /* Fixed width for the boxes (small size) */
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: rgb(255, 255, 0);
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Shadow effect */
  cursor: pointer;
}

.boxHeader {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.box p {
  font-size: 14px;
  color: #555;
}

.row .box:hover {
  background-color: #f9f9f9; /* Hover effect for boxes */
  transform: scale(1.05); /* Slight zoom effect */
  transition: all 0.3s ease-in-out; /* Smooth transition */
}

/* Container override so the rounded card isn't clipped */
.prettyDialogContainer :global(.ms-Dialog-main) {
  border-radius: 16px;
  overflow: hidden;
}

/* Dialog shell */
.prettyDialog {
  background: #fff;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

/* Header */
.pdHeader {
  position: relative;
  padding: 20px 24px 8px;
}
.pdTitle {
  margin: 0;
  font-size: 20px;
  font-weight: 800;
  color: #0f172a; /* slate-900 */
}
.pdClose {
  position: absolute;
  right: 14px;
  top: 12px;
  width: 34px;
  height: 34px;
  border-radius: 10px;
  border: none;
  background: #f1f5f9; /* slate-100 */
  font-size: 20px;
  line-height: 1;
  cursor: pointer;
  transition: background 0.2s ease;
}
.pdClose:hover {
  background: #e2e8f0;
}

/* Body */
.pdBody {
  padding: 6px 24px 0;
}
.pdSubtitle {
  margin: 0 0 14px 0;
  color: #334155; /* slate-700 */
  font-size: 14px;
}
.pdCard {
  border: 1px solid #e5e7eb; /* gray-200 */
  background: #fafafa;
  padding: 14px 16px;
  border-radius: 12px;
}
.pdText {
  margin: 0 0 8px 0;
  color: #374151; /* gray-700 */
  font-size: 14px;
}
.pdLink {
  display: inline-block;
  font-weight: 600;
  text-decoration: none;
  color: #0ea5e9; /* sky-500 */
  margin-bottom: 8px;
}
.pdLink:hover {
  text-decoration: underline;
}

.pdHint {
  margin: 8px 0 4px;
  color: #64748b; /* slate-500 */
  font-size: 13px;
}
.pdMail {
  display: inline-block;
  font-weight: 600;
  color: #111827; /* gray-900 */
  text-decoration: none;
}
.pdMail:hover {
  text-decoration: underline;
}

/* Footer */
.pdFooter {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 18px 24px 22px;
}

/* Buttons */
.pdBtnGhost {
  appearance: none;
  border: 1px solid #e5e7eb;
  background: #fff;
  border-radius: 12px;
  padding: 10px 16px;
  font-weight: 600;
  cursor: pointer;
}
.pdBtnGhost:hover {
  background: #f9fafb;
}

.pdBtnPrimary {
  appearance: none;
  border: none;
  border-radius: 12px;
  padding: 10px 16px;
  font-weight: 700;
  color: #fff;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff8a65, #ff6e7f);
  box-shadow: 0 6px 16px rgba(255, 110, 127, 0.25);
  cursor: pointer;
  transition: transform 0.06s ease, box-shadow 0.2s ease, opacity 0.2s ease;
}
.pdBtnPrimary:hover {
  box-shadow: 0 8px 18px rgba(255, 110, 127, 0.32);
}
.pdBtnPrimary:active {
  transform: translateY(1px);
}

/* ===== Remove Fluent default close icon + extra paddings ===== */
.pdModal :global(.ms-Dialog-main) {
  border-radius: 20px;
  overflow: hidden;
}
.pdModal :global(.ms-Dialog-button--close) {
  display: none !important; /* hides the default "X" so we don't have two */
}
.pdContent :global(.ms-Dialog-inner) {
  padding: 0 !important; /* we'll control spacing ourselves */
}
.pdContent :global(.ms-Dialog-content) {
  padding: 0 !important;
}

/* ===== Our dialog shell ===== */
.prettyDialog {
  background: #ffffff;
}

/* Header */
.pdHeader {
  position: relative;
  padding: 22px 24px 6px;
}
.pdTitle {
  margin: 0;
  font-size: 22px;
  font-weight: 800;
  color: #0b1220; /* deep navy for stronger headline */
  letter-spacing: 0.1px;
}

/* Custom close (pill) */
.pdClose {
  position: absolute;
  right: 16px;
  top: 16px;
  width: 36px;
  height: 36px;
  border-radius: 12px;
  border: none;
  background: #eef2f7;
  font-size: 20px;
  line-height: 1;
  cursor: pointer;
  color: #0b1220;
  transition: background 0.2s ease, transform 0.06s ease;
}
.pdClose:hover {
  background: #e6ebf2;
}
.pdClose:active {
  transform: translateY(1px);
}

/* Body */
.pdBody {
  padding: 8px 24px 0;
}
.pdSubtitle {
  margin: 0 0 16px 0;
  color: #475569; /* slate-600 */
  font-size: 14.5px;
  line-height: 1.55;
}

/* Inner card (subtle, no heavy drop shadow) */
.pdCard {
  border: 1px solid #e6e9ee; /* light border */
  background: #fbfcfe; /* very faint tint */
  padding: 14px 16px;
  border-radius: 14px;
}
.pdText {
  margin: 0 0 6px 0;
  color: #384152; /* gray-ish navy */
  font-size: 14px;
}
.pdLink {
  display: inline-block;
  font-weight: 700;
  text-decoration: none;
  color: #0284c7; /* sky-600 */
  margin-bottom: 2px;
}
.pdLink:hover {
  text-decoration: underline;
}

/* Footer */
.pdFooter {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 18px 24px 24px;
}

/* Buttons */
.pdBtnGhost {
  appearance: none;
  border: 1px solid #e6e9ee;
  background: #ffffff;
  color: #0b1220;
  border-radius: 14px;
  padding: 10px 18px;
  font-weight: 700;
  cursor: pointer;
  transition: background 0.15s ease, transform 0.06s ease;
}
.pdBtnGhost:hover {
  background: #f7f9fc;
}
.pdBtnGhost:active {
  transform: translateY(1px);
}

.pdBtnPrimary {
  appearance: none;
  border: none;
  border-radius: 14px;
  padding: 10px 18px;
  font-weight: 800;
  color: #fff;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff8a65, #ff6e7f);
  box-shadow: 0 10px 22px rgba(255, 110, 127, 0.28);
  cursor: pointer;
  transition: transform 0.06s ease, box-shadow 0.2s ease, filter 0.2s ease;
}
.pdBtnPrimary:hover {
  box-shadow: 0 12px 26px rgba(255, 110, 127, 0.34);
  filter: saturate(1.03);
}
.pdBtnPrimary:active {
  transform: translateY(1px);
}

/* Subtle outer spacing so the content breathes on small screens */
@media (max-width: 480px) {
  .pdHeader {
    padding: 18px 18px 4px;
  }
  .pdBody {
    padding: 6px 18px 0;
  }
  .pdFooter {
    padding: 16px 18px 20px;
  }
}

/* Enhanced Modal Styles */
.enhancedModalOverlay {
  position: fixed;
  inset: 0;
  background: rgba(15, 23, 42, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhancedModalOverlay.fadeIn {
  opacity: 1;
}

.enhancedModal {
  background: #ffffff;
  width: min(600px, 90vw);
  max-height: 85vh;
  border-radius: 20px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  outline: none;
  overflow: hidden;
  transform: scale(0.95) translateY(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.enhancedModal.slideIn {
  transform: scale(1) translateY(0);
}

.modalHeaderGradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
}

.enhancedModalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 28px 20px;
  background: linear-gradient(135deg, #fafbff 0%, #f8fafc 100%);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
}

.modalTitleContainer {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modalIcon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.enhancedModalTitle {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  color: #1e293b;
  letter-spacing: -0.025em;
}

.enhancedCloseBtn {
  appearance: none;
  border: none;
  background: rgba(148, 163, 184, 0.1);
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s ease;
}

.enhancedCloseBtn:hover {
  background: rgba(148, 163, 184, 0.2);
  color: #334155;
  transform: scale(1.05);
}

.enhancedModalBody {
  padding: 28px;
  max-height: calc(85vh - 200px);
  overflow-y: auto;
}

.modalContent {
  color: #475569;
  line-height: 1.7;
}

.modalContentCard {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.modalSection {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.modalSectionTitle {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: "";
    width: 4px;
    height: 16px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 2px;
  }
}

.modalText {
  margin: 0 0 16px 0;
  color: #475569;
  line-height: 1.7;
  font-size: 15px;

  &:last-child {
    margin-bottom: 0;
  }
}

.modalLink {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    color: #2563eb;
    text-decoration: underline;
  }

  &::after {
    content: "↗";
    margin-left: 4px;
    font-size: 12px;
    opacity: 0.7;
  }
}

.enhancedModalActions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 28px 28px;
  background: linear-gradient(135deg, #fafbff 0%, #f8fafc 100%);
  border-top: 1px solid rgba(226, 232, 240, 0.8);
}

.enhancedPrimaryBtn {
  appearance: none;
  border: none;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  font-weight: 600;
  font-size: 14px;
  border-radius: 12px;
  padding: 12px 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3), 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4), 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: scale(0.95) translateY(20px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.fadeIn {
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slideIn {
  animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive design */
@media (max-width: 640px) {
  .enhancedModal {
    width: 95vw;
    margin: 20px;
  }

  .enhancedModalHeader {
    padding: 20px 20px 16px;
  }

  .enhancedModalBody {
    padding: 20px;
  }

  .enhancedModalActions {
    padding: 16px 20px 20px;
  }

  .modalTitleContainer {
    gap: 10px;
  }

  .modalIcon {
    width: 36px;
    height: 36px;
  }

  .enhancedModalTitle {
    font-size: 20px;
  }
}
