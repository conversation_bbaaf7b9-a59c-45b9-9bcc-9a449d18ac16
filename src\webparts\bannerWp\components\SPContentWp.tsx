import * as React from 'react';
import styles from './SPContentWp.module.scss';
import { useLocale } from '../../../common/useLocale';

type SPBox = { id: number; title: string; content: string };

const SPContentWp: React.FC = () => {
  const { strings } = useLocale();

  // ✅ Prefer JSON-driven content; keep your current texts as safe fallbacks
  const boxes: SPBox[] =
    strings?.boxes ?? [
      {
        id: 1,
        title: 'What is PLATON?',
        content:
          'PLATON is our new Product Data Management (PDM) system, built on Siemens Teamcenter, and will replace Smaragd starting February 2026.',
      },
      {
        id: 2,
        title: 'Why PLATON?',
        content:
          'Following the spin-off from Mercedes-Benz, we are in the process of building our own independent engineering landscape. This transformation also impacts our current PDM system, SMARAGD.',
      },
      {
        id: 3,
        title: 'When is the Rollout?',
        content:
          'PLATON is being rolled out across all segments and business units. According to the latest timeline, the complete rollout is planned by February 2026.',
      },
    ];

  return (
    <div className={styles.boxContainer}>
      {boxes.map((box) => (
        <div key={box.id} className={styles.box}>
          <div className={styles.header}>{box.title}</div>
          <div className={styles.content}>{box.content}</div>
        </div>
      ))}
    </div>
  );
};

export default SPContentWp;
