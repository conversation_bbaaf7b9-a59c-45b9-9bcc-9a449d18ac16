const enStrings = {
  boxes: [
    {
      id: 1,
      title: "What is PLATON?",
      content:
        "PLATON is our new Product Data Management (PDM) system, built on Siemens Teamcenter, and will replace Smaragd starting February 2026."
    },
    {
      id: 2,
      title: "Why PLATON?",
      content:
        "Following the spin-off from Mercedes-Benz, we are in the process of building our own independent engineering landscape. This transformation also impacts our current PDM system, SMARAGD."
    },
    {
      id: 3,
      title: "When is the Rollout?",
      content:
        "PLATON is being rolled out across all segments and business units. According to the latest timeline, the complete rollout is planned by February 2026."
    }
  ],
  essentialsHeader: "My Essentials Web Part",
  /** Use stable ids; omit url for dialog tiles */
  essentialsBoxes: [
    {
      id: "training",
      title: "Training Videos",
      url: "https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"
    },
    {
      id: "manuals",
      title: "PLATON Manuals",
      url: "https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"
    },
    {
      id: "access",
      title: "Access PLATON !",
      url: "https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"
    },
    { id: "xcellerator", title: "Teamcenter Xcelerator" },
    { id: "dtag", title: "PLATON DTAG Portal" },
    {
      id: "contact",
      title: "Contact us",
      url: "https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/ContactUs.aspx"
    }
  ],
  startHeader: "How to start PLATON?",
  startCards: [
    { content: "Take your first step" },
    { content: "Contact us, or we’ll reach out to you soon" },
    { content: "Get the PLATON Basic private lesson" },
    { content: "Get access to the PLATON environment and play around" }
  ],
  banner: { noImage: "No image available" },
  ctaCards: [
    {
      title: "Test Environment",
      image:
        "https://corptb.sharepoint.com/sites/ProjectEngLand/SiteAssets/PlatonImages/PLATON%20ICON/StartingPointIcons/Down/icon-testing-videos.svg",
      link: "https://pre04.platon.daimlertruck.com/awc/#/showHome"
    },
    {
      title: "Training Environment",
      image:
        "https://corptb.sharepoint.com/sites/ProjectEngLand/SiteAssets/PlatonImages/PLATON%20ICON/StartingPointIcons/Down/icon-test-environment.svg",
      link: "https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/BlockYourTraining.aspx"
    },
    {
      title: "ADO",
      image:
        "https://corptb.sharepoint.com/sites/ProjectEngLand/SiteAssets/PlatonImages/PLATON%20ICON/StartingPointIcons/Down/icon-ado.svg",
      link:
        "https://dev.azure.com/CORPTB/104%20newPDM/_testPlans/execute?planId=337496&suiteId=337497"
    },
    {
      title: "NX",
      image:
        "https://corptb.sharepoint.com/sites/ProjectEngLand/SiteAssets/PlatonImages/PLATON%20ICON/StartingPointIcons/Down/icon-nx.svg",
      link: "",
      action: "dialog-nx"
    }
  ],
  dialogs: {
    xcellerator: {
      title: "Teamcenter Xcelerator",
      p1:
        "Welcome to the Siemens Xcelerator Academy! Please go through our training snippet videos to get trained with the various modules of the PLATON.",
      linkText: "Siemens Xcelerator Academy",
      p2Suffix: "Click here to explore the Training Materials.",
      p3:
        "If you can’t access this Siemens Page, please feel free to write to us.",
      supportLinkText: "PLATON Support Team",
      close: "Close"
    },
    dtag: {
      title: "PLATON DTAG Portal",
      p1:
        "Hello PLATON Users! Welcome to the PLATON DTAG Portal. Please stay tuned with the Central team for more information regarding the latest updates and changes in the PLATON environment.",
      p2:
        "Please stay tuned to the following DTAG Teams channel to get further updates.",
      linkText: "DTAG Teams Channel",
      close: "Close"
    },
    nxDialog: {
      title: "NX",
      intro:
        "Hello PLATON Users! Please use this NX Environment to perform the designing of parts, assemblies and other components.",
      installNote:
        "In order to install and get access to the NX environment, please refer to the following manuals.",
      win11: "For Windows 11 Users:",
      win11Link: "Connect to PLATON AVD - Windows 11.pdf",
      win10: "For Windows 10 Users:",
      win10Link: "Connect to PLATON AVD - Windows 10.pdf",
      closeButton: "Close"
    }
  }
};

export default enStrings;
