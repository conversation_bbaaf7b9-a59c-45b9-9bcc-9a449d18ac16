import * as React from "react";
import { useState, useMemo, useEffect, useRef, useCallback } from "react";
import styles from "./Essentials.module.scss";
import { useLocale } from "../../../common/useLocale";

/** Stable ids, never localized */
type BoxId =
  | "training"
  | "manuals"
  | "access"
  | "xcellerator"
  | "dtag"
  | "contact";

type EssentialsBox = {
  id: BoxId;
  title: string; // localized label
  url?: string; // if present -> navigate
};

/** Modern dialog matching the clean design from the image */
function Modal({
  open,
  title,
  children,
  onClose,
}: {
  open: boolean;
  title: string;
  children: React.ReactNode;
  onClose: () => void;
}): JSX.Element | null {
  const firstBtnRef = useRef<HTMLButtonElement | null>(null);

  const handleEsc = useCallback(
    (e: KeyboardEvent) => {
      if (!open) return;
      if (e.key === "Escape") onClose();
    },
    [open, onClose]
  );

  const handleTabTrap = useCallback(
    (e: KeyboardEvent) => {
      if (!open) return;
      const modal = document.querySelector(
        `.${styles.modernModal}`
      ) as HTMLElement | null;
      if (!modal) return;
      const focusables = modal.querySelectorAll<HTMLElement>(
        'button,[href],input,textarea,select,[tabindex]:not([tabindex="-1"])'
      );
      if (!focusables.length) return;
      const first = focusables[0];
      const last = focusables[focusables.length - 1];

      if (e.key === "Tab") {
        if (e.shiftKey && document.activeElement === first) {
          last.focus();
          e.preventDefault();
        } else if (!e.shiftKey && document.activeElement === last) {
          first.focus();
          e.preventDefault();
        }
      }
    },
    [open]
  );

  useEffect(() => {
    if (!open) return;

    // Add body scroll lock
    document.body.style.overflow = "hidden";

    // Focus management
    setTimeout(() => {
      firstBtnRef.current?.focus();
    }, 100);

    document.addEventListener("keydown", handleEsc);
    document.addEventListener("keydown", handleTabTrap);

    return () => {
      document.body.style.overflow = "unset";
      document.removeEventListener("keydown", handleEsc);
      document.removeEventListener("keydown", handleTabTrap);
    };
  }, [open, handleEsc, handleTabTrap]);

  if (!open) return null;

  return (
    <div
      className={`${styles.modernModalOverlay} ${open ? styles.fadeIn : ""}`}
      role="presentation"
      onClick={onClose}
    >
      <div
        className={`${styles.modernModal} ${open ? styles.slideIn : ""}`}
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
        onClick={(e): void => e.stopPropagation()}
      >
        <div className={styles.modernModalContent}>
          <h2 id="modal-title" className={styles.modernModalTitle}>
            {title}
          </h2>

          <div className={styles.modernModalBody}>{children}</div>

          <div className={styles.modernModalActions}>
            <button
              ref={firstBtnRef}
              className={styles.modernCloseBtn}
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}


const EssentialsWP: React.FC = () => {
  const { strings } = useLocale();
  const [isXcelleratorOpen, setXcelleratorOpen] = useState(false);
  const [isDtagOpen, setDtagOpen] = useState(false);

  const headerText: string =
    strings?.essentialsHeader ?? "My Essentials Web Part";

  // Use localized strings, but branch by stable id
  const boxLinks: EssentialsBox[] = useMemo(
    () =>
      (strings?.essentialsBoxes as EssentialsBox[]) ?? [
        {
          id: "training",
          title: "Training Videos",
          url: "https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx",
        },
        {
          id: "manuals",
          title: "PLATON Manuals",
          url: "https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx",
        },
        {
          id: "access",
          title: "Access PLATON !",
          url: "https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx",
        },
        { id: "xcellerator", title: "Teamcenter Xcelerator" }, // dialog
        { id: "dtag", title: "PLATON DTAG Portal" }, // dialog
        {
          id: "contact",
          title: "Contact us",
          url: "https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/ContactUs.aspx",
        },
      ],
    [strings]
  );

  /** Open dialogs for id'ed tiles; otherwise navigate */
  const handleBoxClick = (box: EssentialsBox): void => {
    switch (box.id) {
      case "xcellerator":
        setXcelleratorOpen(true);
        return;
      case "dtag":
        setDtagOpen(true);
        return;
      default:
        if (box.url) window.location.href = box.url;
        return;
    }
  };

  const onKeyBox = (
    e: React.KeyboardEvent<HTMLDivElement>,
    box: EssentialsBox
  ): void => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleBoxClick(box);
    }
  };

  return (
    <div className={styles.wrapper}>
      <div className={styles.header}>
        <h1>{headerText}</h1>
      </div>

      <div className={styles.sixBoxes}>
        <div className={styles.row}>
          {boxLinks.slice(0, 3).map((box) => (
            <div
              key={box.id}
              className={styles.box}
              role="button"
              tabIndex={0}
              aria-label={box.title}
              onClick={() => handleBoxClick(box)}
              onKeyDown={(e) => onKeyBox(e, box)}
            >
              <h2 className={styles.boxHeader}>{box.title}</h2>
            </div>
          ))}
        </div>

        <div className={styles.row}>
          {boxLinks.slice(3, 6).map((box) => (
            <div
              key={box.id}
              className={styles.box}
              role="button"
              tabIndex={0}
              aria-label={box.title}
              onClick={() => handleBoxClick(box)}
              onKeyDown={(e) => onKeyBox(e, box)}
            >
              <h2 className={styles.boxHeader}>{box.title}</h2>
            </div>
          ))}
        </div>
      </div>

      {/* Teamcenter Xcelerator Modal */}
      <Modal
        open={isXcelleratorOpen}
        onClose={() => setXcelleratorOpen(false)}
        title={strings?.dialogs?.xcellerator?.title ?? "Teamcenter Xcelerator"}
      >
        <p>
          {strings?.dialogs?.xcellerator?.p1 ??
            "Welcome to Siemens Xcelerator Academy. Please watch the training snippet videos to learn about each module of PLATON."}
        </p>
        <p>
          Access the{" "}
          <a
            href="https://training.plm.automation.siemens.com/index.cfm"
            target="_blank"
            rel="noreferrer"
          >
            {strings?.dialogs?.xcellerator?.linkText ??
              "Siemens Xcelerator Academy"}
          </a>{" "}
          {strings?.dialogs?.xcellerator?.p2Suffix ?? "for training materials."}
        </p>
        <p>
          {strings?.dialogs?.xcellerator?.p3 ??
            "If you cannot access the Siemens page, please feel free to contact us."}
        </p>
        <p>
          Contact our{" "}
          <a href="mailto:<EMAIL>">
            {strings?.dialogs?.xcellerator?.supportLinkText ??
              "PLATON Support Team"}
          </a>{" "}
          for assistance with training and access.
        </p>
      </Modal>

      {/* PLATON DTAG Portal Modal */}
      <Modal
        open={isDtagOpen}
        onClose={() => setDtagOpen(false)}
        title={strings?.dialogs?.dtag?.title ?? "PLATON DTAG Portal"}
      >
        <p>
          {strings?.dialogs?.dtag?.p1 ??
            "Hello PLATON Users! Welcome to the PLATON DTAG Portal. Please check the announcements from the central team for the latest updates and changes in the PLATON environment."}
        </p>
        <p>
          {strings?.dialogs?.dtag?.p2 ??
            "For future updates, please check the following DTAG Teams channel."}
        </p>
        <p>
          Join the{" "}
          <a
            href="https://teams.microsoft.com/l/team/19%3AII8Szlz3W1W_MDqN3WRitf0zEf383pwg_mJiNrVeNeU1%40thread.tacv2/conversations?groupId=d6b9862a-1d93-4f94-a67e-d3413c9e8e3c&tenantId=505cca53-5750-4134-9501-8d52d5df3cd1"
            target="_blank"
            rel="noreferrer"
          >
            {strings?.dialogs?.dtag?.linkText ?? "DTAG Teams Channel"}
          </a>{" "}
          to collaborate with your team and get real-time updates.
        </p>
      </Modal>
    </div>
  );
};

export default EssentialsWP;
