import * as React from "react";
import styles from "./BannerWp.module.scss";
import type { IBannerWpProps } from "./IBannerWpProps";
import { useEffect, useState } from "react";
import { sp } from "@pnp/sp/presets/all";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/autoplay";
import { ISPFXContext } from "@pnp/common";
import SPContent from "./SPContentWp";
import Start from "./StartWp";
import Essentials from "./Essentials";
import { useLocale } from "../../../common/useLocale";
import {
  Dialog,
  DialogType,
  DialogFooter,
  DefaultButton /*, PrimaryButton */,
} from "@fluentui/react";

interface IBannerItem {
  Title: string;
  ImageURL: string;
}

interface CardItem {
  title: string;
  image: string;
  link?: string;
}

interface SPBannerListItem {
  Title: string;
  ImageUrl?: string | { Url?: string };
  Created?: string;
}

interface CtaCard {
  title: string;
  image: string;
  link?: string;
}

const BannerWp: React.FC<IBannerWpProps> = (props) => {
  const [banners, setBanners] = useState<IBannerItem[]>([]);
  const [swiperReady, setSwiperReady] = useState(false);

  // NEW: NX dialog state
  const [isNxOpen, setIsNxOpen] = useState(false);

  const { strings } = useLocale();

  const cards: CardItem[] = ((strings.ctaCards as CtaCard[] | undefined) ?? []).map((c) => ({
    title: c.title,
    image: c.image,
    link: c.link,
  }));

  const handleCardClick = (card: CardItem): void => {
    const t = (card.title || "").trim().toLowerCase();
    // Open dialog for NX
    if (t === "nx") {
      setIsNxOpen(true);
      return;
    }
    if (!card.link) return;

    if (/^https?:\/\//i.test(card.link)) {
      window.open(card.link, "_blank", "noopener,noreferrer");
    } else {
      window.location.href = card.link;
    }
  };

  const isCardInteractive = (card: CardItem): boolean => {
    // Make NX clickable even without a link
    return !!card.link || (card.title || "").trim().toLowerCase() === "nx";
  };

  useEffect(() => {
    sp.setup({ spfxContext: props.context as unknown as ISPFXContext });

    const fetchBanners = async (): Promise<void> => {
      try {
        const items = (await sp.web.lists
          .getByTitle("StartingPointBannerImages")
          .items.select("Title", "ImageUrl", "Created")
          .orderBy("Created", false)
          .top(3)
          .get()) as SPBannerListItem[];

        const formatted: IBannerItem[] = items.map((item) => {
          let imageURL = "";
          const img = item.ImageUrl;
          if (img && typeof img === "object" && img.Url) imageURL = img.Url;
          else if (typeof img === "string") imageURL = img;
          return { Title: item.Title, ImageURL: imageURL };
        });

        setBanners(formatted);
        setSwiperReady(true);
      } catch (error) {
        console.error("Error fetching banners:", error);
        setSwiperReady(false);
      }
    };

    fetchBanners().catch((err) => {
      console.error("Unhandled fetchBanners error:", err);
    });
  }, [props.context]);

  return (
    <div>
      <div className={styles.carouselWrapper}>
        {swiperReady && (
          <Swiper
            modules={[Autoplay, Pagination]}
            spaceBetween={30}
            pagination={{ clickable: true }}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            loop
            grabCursor
          >
            {banners.map((slide, index) => (
              <SwiperSlide key={index}>
                {slide.ImageURL ? (
                  <img
                    src={slide.ImageURL}
                    alt={slide.Title}
                    className={styles.carouselImage}
                    onError={(e) => (e.currentTarget.style.display = "none")}
                  />
                ) : (
                  <div>{strings.banner?.noImage || "No image available"}</div>
                )}
              </SwiperSlide>
            ))}
          </Swiper>
        )}
      </div>

      <SPContent />
      <div>
        <Start />
      </div>
      <div>
        <Essentials />
      </div>

      <div className={styles.wrapper2}>
        <div className={styles.cardRow}>
          {cards.map((card) => {
            const interactive = isCardInteractive(card);
            return (
              <div
                key={card.title}
                className={styles.card}
                role={interactive ? "button" : undefined}
                tabIndex={interactive ? 0 : -1}
                aria-label={card.title}
                onClick={() => interactive && handleCardClick(card)}
                onKeyDown={(e) => {
                  if (interactive && (e.key === "Enter" || e.key === " ")) {
                    e.preventDefault();
                    handleCardClick(card);
                  }
                }}
              >
                <img
                  src={card.image}
                  alt={card.title}
                  className={styles.cardImage}
                />
                <h3 className={styles.cardTitle}>{card.title}</h3>
              </div>
            );
          })}
        </div>
      </div>

      {/* NX Dialog */}
      <Dialog
        hidden={!isNxOpen}
        onDismiss={() => setIsNxOpen(false)}
        dialogContentProps={{
          type: DialogType.normal,
          title: "NX",
        }}
        modalProps={{ isBlocking: true }}
      >
        {/* Example content */}
        <p>
          {strings.dialogs.nxDialog.intro}
        </p>
        <p>
          {strings.dialogs.nxDialog.installNote}
        </p>
        <p>
          {strings.dialogs.nxDialog.win11}{" "}
          <a href="https://corptb.sharepoint.com/sites/ProjectEngLand/PLATON%20Testing/AVD(Azure%20Virtual%20Desktop)%20Access%20Manual/Connect%20to%20PLATON%20AVD%20-%20Windows%2011.pdf?csf=1&web=1&e=38Yh7n" target="_blank" rel="noreferrer">
          {strings.dialogs.nxDialog.win11Link}</a>{" "}
        </p>
        <p>
          {strings.dialogs.nxDialog.win10}{" "}
          <a href="https://corptb.sharepoint.com/sites/ProjectEngLand/PLATON%20Testing/AVD(Azure%20Virtual%20Desktop)%20Access%20Manual/Connect%20to%20PLATON%20AVD%20-%20Windows%2011.pdf?csf=1&web=1&e=38Yh7n" target="_blank" rel="noreferrer">
          {strings.dialogs.nxDialog.win10Link}</a>{" "}
        </p>
        {/* Optional CTA to a doc/download once available */}
        {/* <DialogFooter>
          <PrimaryButton text="Open NX Guide" onClick={() => window.open('https://example.com/nx-guide', '_blank', 'noopener,noreferrer')} />
          <DefaultButton text="Close" onClick={() => setIsNxOpen(false)} />
        </DialogFooter> */}
        <DialogFooter>
          <DefaultButton text={strings.dialogs.nxDialog.closeButton} onClick={() => setIsNxOpen(false)} />
        </DialogFooter>
      </Dialog>
    </div>
  );
};

export default BannerWp;
