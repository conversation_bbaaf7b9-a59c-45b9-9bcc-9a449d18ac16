{"extends": "./node_modules/@microsoft/rush-stack-compiler-4.7/includes/tsconfig-web.json", "compilerOptions": {"target": "es5", "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "jsx": "react", "declaration": true, "sourceMap": true, "experimentalDecorators": true, "skipLibCheck": true, "outDir": "lib", "inlineSources": false, "noImplicitAny": true, "typeRoots": ["./node_modules/@types", "./node_modules/@microsoft"], "types": ["webpack-env"], "lib": ["es5", "dom", "es2015.collection", "es2015.promise", "es2015"]}, "include": ["src/**/*.ts", "src/**/*.tsx"]}