.boxContainer {
    display: flex;
    gap: 20px;
    justify-content: space-between;
    margin-top: 20px;
  }
  
  .box {
    width: 30%;
    background: #fff;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.48);
    font-size: large;
    display: flex;
    flex-direction: column;
    position: relative;
  
    &:hover .content {
      opacity: 1;
      transform: translateY(0);
      pointer-events: auto;
    }
  }
  
  .header {
    background-color: rgb(255 255 0);
    padding: 12px;
    font-weight: bold;
    cursor: pointer;
  }
  
  .content {
    background-color: black;
    padding: 12px;
    font-size: large;
    line-height: 1.6;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    opacity: 0;
    transform: translateY(-20px);
    pointer-events: none;
    transition: opacity 0.3s ease, transform 0.3s ease;
    color: #fff;
    z-index: 9999;
  }
  